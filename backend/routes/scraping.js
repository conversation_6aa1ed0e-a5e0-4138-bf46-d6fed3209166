const express = require('express');
const router = express.Router();
const scrapingService = require('../services/scrapingService');

// TikTok scraping endpoint
router.post('/tiktok', async (req, res) => {
  try {
    const { url } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    if (!url.includes('tiktok.com')) {
      return res.status(400).json({ error: 'Invalid TikTok URL' });
    }

    console.log(`🎵 Scraping TikTok URL: ${url}`);
    const data = await scrapingService.scrapeTikTok(url);
    
    res.json({
      success: true,
      platform: 'TikTok',
      url: url,
      data: data,
      scrapedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('TikTok scraping error:', error);
    res.status(500).json({ 
      error: 'Failed to scrape TikTok data', 
      message: error.message 
    });
  }
});

// Instagram scraping endpoint
router.post('/instagram', async (req, res) => {
  try {
    const { url } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    if (!url.includes('instagram.com')) {
      return res.status(400).json({ error: 'Invalid Instagram URL' });
    }

    console.log(`📸 Scraping Instagram URL: ${url}`);
    const data = await scrapingService.scrapeInstagram(url);
    
    res.json({
      success: true,
      platform: 'Instagram',
      url: url,
      data: data,
      scrapedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Instagram scraping error:', error);
    res.status(500).json({ 
      error: 'Failed to scrape Instagram data', 
      message: error.message 
    });
  }
});

module.exports = router;
