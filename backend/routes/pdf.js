const express = require('express');
const router = express.Router();
const pdfService = require('../services/pdfService');

// Generate PDF from JSON data
router.post('/generate', async (req, res) => {
  try {
    const { data, title = 'Social Media Report' } = req.body;
    
    if (!data) {
      return res.status(400).json({ error: 'Data is required for PDF generation' });
    }

    console.log(`📄 Generating PDF: ${title}`);
    
    const pdfBuffer = await pdfService.generatePDF(data, title);
    
    // Set headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);
    
    res.send(pdfBuffer);

  } catch (error) {
    console.error('PDF generation error:', error);
    res.status(500).json({ 
      error: 'Failed to generate PDF', 
      message: error.message 
    });
  }
});

// Get sample JSON structure for PDF generation
router.get('/sample', (req, res) => {
  const sampleData = {
    title: "Social Media Performance Report",
    date: new Date().toISOString().split('T')[0],
    summary: {
      totalViews: 125000,
      totalLikes: 8500,
      totalComments: 450,
      totalShares: 320
    },
    platforms: [
      {
        name: "TikTok",
        url: "https://tiktok.com/@username/video/123456789",
        metrics: {
          views: 75000,
          likes: 5200,
          comments: 280,
          shares: 190
        },
        description: "Viral dance video that gained massive traction"
      },
      {
        name: "Instagram",
        url: "https://instagram.com/p/ABC123DEF456/",
        metrics: {
          views: 50000,
          likes: 3300,
          comments: 170,
          shares: 130
        },
        description: "Behind-the-scenes content from latest photoshoot"
      }
    ],
    insights: [
      "TikTok content performed 50% better than Instagram",
      "Dance videos generate highest engagement",
      "Peak engagement time: 7-9 PM EST"
    ]
  };

  res.json({
    success: true,
    message: "Sample JSON structure for PDF generation",
    data: sampleData
  });
});

module.exports = router;
