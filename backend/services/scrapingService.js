const puppeteer = require('puppeteer');
const cheerio = require('cheerio');
const axios = require('axios');

class ScrapingService {
  
  async scrapeTikTok(url) {
    try {
      // Launch browser with specific settings for TikTok
      const browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();
      
      // Set user agent to avoid detection
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      // Navigate to TikTok URL
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      
      // Wait for content to load
      await page.waitForTimeout(3000);
      
      // Extract data using page.evaluate
      const data = await page.evaluate(() => {
        // Try to extract basic information
        const title = document.querySelector('[data-e2e="browse-video-desc"]')?.textContent || 
                     document.querySelector('h1')?.textContent || 
                     'TikTok Video';
        
        const author = document.querySelector('[data-e2e="browse-username"]')?.textContent ||
                      document.querySelector('[data-e2e="video-author-uniqueid"]')?.textContent ||
                      'Unknown Author';
        
        // Try to extract metrics (these selectors might need updates)
        const likes = document.querySelector('[data-e2e="browse-like-count"]')?.textContent ||
                     document.querySelector('[data-e2e="like-count"]')?.textContent ||
                     '0';
        
        const comments = document.querySelector('[data-e2e="browse-comment-count"]')?.textContent ||
                        document.querySelector('[data-e2e="comment-count"]')?.textContent ||
                        '0';
        
        const shares = document.querySelector('[data-e2e="browse-share-count"]')?.textContent ||
                      document.querySelector('[data-e2e="share-count"]')?.textContent ||
                      '0';
        
        return {
          title: title.trim(),
          author: author.trim(),
          likes: likes.trim(),
          comments: comments.trim(),
          shares: shares.trim(),
          views: 'N/A' // TikTok doesn't always show view counts publicly
        };
      });

      await browser.close();
      
      // Return formatted data
      return {
        ...data,
        platform: 'TikTok',
        extractedAt: new Date().toISOString(),
        note: 'Data extracted using web scraping. Some metrics may not be available due to TikTok\'s privacy settings.'
      };

    } catch (error) {
      console.error('TikTok scraping error:', error);
      
      // Return mock data if scraping fails
      return {
        title: 'TikTok Video (Demo Data)',
        author: '@demo_user',
        likes: '12.5K',
        comments: '834',
        shares: '267',
        views: '125.3K',
        platform: 'TikTok',
        extractedAt: new Date().toISOString(),
        note: 'This is demo data. Real scraping may be limited due to TikTok\'s anti-bot measures.',
        error: 'Scraping failed, showing demo data'
      };
    }
  }

  async scrapeInstagram(url) {
    try {
      // Launch browser for Instagram
      const browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();
      
      // Set user agent
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      // Navigate to Instagram URL
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      
      // Wait for content to load
      await page.waitForTimeout(3000);
      
      // Extract data
      const data = await page.evaluate(() => {
        // Try to extract post information
        const description = document.querySelector('meta[property="og:description"]')?.content ||
                           document.querySelector('[data-testid="post-description"]')?.textContent ||
                           'Instagram Post';
        
        const author = document.querySelector('meta[property="og:title"]')?.content?.split('•')[0]?.trim() ||
                      'Unknown Author';
        
        // Try to extract metrics (Instagram makes this challenging)
        const likes = document.querySelector('button[data-testid="like"] + span')?.textContent ||
                     document.querySelector('span[data-testid="like-count"]')?.textContent ||
                     '0';
        
        const comments = document.querySelector('button[data-testid="comment"] + span')?.textContent ||
                        '0';
        
        return {
          description: description.trim(),
          author: author.trim(),
          likes: likes.trim(),
          comments: comments.trim(),
          shares: 'N/A', // Instagram doesn't show share counts
          views: 'N/A'   // Instagram doesn't always show view counts
        };
      });

      await browser.close();
      
      return {
        ...data,
        platform: 'Instagram',
        extractedAt: new Date().toISOString(),
        note: 'Data extracted using web scraping. Some metrics may not be available due to Instagram\'s privacy settings.'
      };

    } catch (error) {
      console.error('Instagram scraping error:', error);
      
      // Return mock data if scraping fails
      return {
        description: 'Instagram Post (Demo Data)',
        author: '@demo_user',
        likes: '8.2K',
        comments: '156',
        shares: 'N/A',
        views: '45.7K',
        platform: 'Instagram',
        extractedAt: new Date().toISOString(),
        note: 'This is demo data. Real scraping may be limited due to Instagram\'s anti-bot measures.',
        error: 'Scraping failed, showing demo data'
      };
    }
  }
}

module.exports = new ScrapingService();
