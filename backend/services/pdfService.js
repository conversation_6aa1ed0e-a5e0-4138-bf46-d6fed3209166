const PDFDocument = require('pdfkit');

class PDFService {
  
  async generatePDF(data, title = 'Social Media Report') {
    return new Promise((resolve, reject) => {
      try {
        // Create a new PDF document
        const doc = new PDFDocument({ margin: 50 });
        const buffers = [];
        
        // Collect PDF data
        doc.on('data', buffers.push.bind(buffers));
        doc.on('end', () => {
          const pdfBuffer = Buffer.concat(buffers);
          resolve(pdfBuffer);
        });
        
        // Add title
        doc.fontSize(24)
           .font('Helvetica-Bold')
           .text(title, { align: 'center' });
        
        doc.moveDown(2);
        
        // Add generation date
        doc.fontSize(12)
           .font('Helvetica')
           .text(`Generated on: ${new Date().toLocaleDateString()}`, { align: 'right' });
        
        doc.moveDown(1);
        
        // Add summary section if available
        if (data.summary) {
          this.addSummarySection(doc, data.summary);
        }
        
        // Add platforms data if available
        if (data.platforms && Array.isArray(data.platforms)) {
          this.addPlatformsSection(doc, data.platforms);
        }
        
        // Add insights if available
        if (data.insights && Array.isArray(data.insights)) {
          this.addInsightsSection(doc, data.insights);
        }
        
        // If data is a simple object (scraped data), format it differently
        if (!data.platforms && !data.summary && !data.insights) {
          this.addSimpleDataSection(doc, data);
        }
        
        // Add footer
        this.addFooter(doc);
        
        // Finalize the PDF
        doc.end();
        
      } catch (error) {
        reject(error);
      }
    });
  }
  
  addSummarySection(doc, summary) {
    doc.fontSize(18)
       .font('Helvetica-Bold')
       .text('Summary', { underline: true });
    
    doc.moveDown(0.5);
    
    doc.fontSize(12)
       .font('Helvetica');
    
    if (summary.totalViews) {
      doc.text(`Total Views: ${this.formatNumber(summary.totalViews)}`);
    }
    if (summary.totalLikes) {
      doc.text(`Total Likes: ${this.formatNumber(summary.totalLikes)}`);
    }
    if (summary.totalComments) {
      doc.text(`Total Comments: ${this.formatNumber(summary.totalComments)}`);
    }
    if (summary.totalShares) {
      doc.text(`Total Shares: ${this.formatNumber(summary.totalShares)}`);
    }
    
    doc.moveDown(2);
  }
  
  addPlatformsSection(doc, platforms) {
    doc.fontSize(18)
       .font('Helvetica-Bold')
       .text('Platform Performance', { underline: true });
    
    doc.moveDown(1);
    
    platforms.forEach((platform, index) => {
      // Platform name
      doc.fontSize(14)
         .font('Helvetica-Bold')
         .text(`${platform.name}`, { continued: false });
      
      // URL
      if (platform.url) {
        doc.fontSize(10)
           .font('Helvetica')
           .fillColor('blue')
           .text(platform.url, { link: platform.url });
        doc.fillColor('black');
      }
      
      doc.moveDown(0.5);
      
      // Metrics
      if (platform.metrics) {
        doc.fontSize(12)
           .font('Helvetica');
        
        if (platform.metrics.views) {
          doc.text(`Views: ${this.formatNumber(platform.metrics.views)}`);
        }
        if (platform.metrics.likes) {
          doc.text(`Likes: ${this.formatNumber(platform.metrics.likes)}`);
        }
        if (platform.metrics.comments) {
          doc.text(`Comments: ${this.formatNumber(platform.metrics.comments)}`);
        }
        if (platform.metrics.shares) {
          doc.text(`Shares: ${this.formatNumber(platform.metrics.shares)}`);
        }
      }
      
      // Description
      if (platform.description) {
        doc.moveDown(0.5);
        doc.fontSize(11)
           .font('Helvetica-Oblique')
           .text(`Description: ${platform.description}`);
      }
      
      // Add separator line if not last item
      if (index < platforms.length - 1) {
        doc.moveDown(1);
        doc.strokeColor('#cccccc')
           .lineWidth(1)
           .moveTo(50, doc.y)
           .lineTo(550, doc.y)
           .stroke();
        doc.moveDown(1);
      }
    });
    
    doc.moveDown(2);
  }
  
  addInsightsSection(doc, insights) {
    doc.fontSize(18)
       .font('Helvetica-Bold')
       .text('Key Insights', { underline: true });
    
    doc.moveDown(1);
    
    insights.forEach((insight, index) => {
      doc.fontSize(12)
         .font('Helvetica')
         .text(`${index + 1}. ${insight}`);
      doc.moveDown(0.5);
    });
    
    doc.moveDown(1);
  }
  
  addSimpleDataSection(doc, data) {
    doc.fontSize(18)
       .font('Helvetica-Bold')
       .text('Scraped Data', { underline: true });
    
    doc.moveDown(1);
    
    doc.fontSize(12)
       .font('Helvetica');
    
    // Display all data properties
    Object.keys(data).forEach(key => {
      if (key !== 'extractedAt' && key !== 'note' && key !== 'error') {
        const value = data[key];
        const displayKey = key.charAt(0).toUpperCase() + key.slice(1);
        doc.text(`${displayKey}: ${value}`);
      }
    });
    
    if (data.note) {
      doc.moveDown(1);
      doc.fontSize(10)
         .font('Helvetica-Oblique')
         .text(`Note: ${data.note}`);
    }
    
    doc.moveDown(2);
  }
  
  addFooter(doc) {
    const bottomMargin = 50;
    const pageHeight = doc.page.height;
    
    doc.fontSize(10)
       .font('Helvetica')
       .fillColor('gray')
       .text('Generated by Social Media Scraper & PDF Generator', 
             50, 
             pageHeight - bottomMargin, 
             { align: 'center' });
  }
  
  formatNumber(num) {
    if (typeof num === 'string') return num;
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }
}

module.exports = new PDFService();
