{"name": "social-scraper-backend", "version": "1.0.0", "description": "Backend for social media scraping and PDF generation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "puppeteer": "^21.5.2", "cheerio": "^1.0.0-rc.12", "axios": "^1.6.2", "pdfkit": "^0.14.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["scraping", "pdf", "tiktok", "instagram"], "author": "", "license": "MIT"}