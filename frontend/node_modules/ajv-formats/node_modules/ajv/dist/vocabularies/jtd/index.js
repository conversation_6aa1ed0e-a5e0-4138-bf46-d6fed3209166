"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ref_1 = require("./ref");
const type_1 = require("./type");
const enum_1 = require("./enum");
const elements_1 = require("./elements");
const properties_1 = require("./properties");
const optionalProperties_1 = require("./optionalProperties");
const discriminator_1 = require("./discriminator");
const values_1 = require("./values");
const union_1 = require("./union");
const metadata_1 = require("./metadata");
const jtdVocabulary = [
    "definitions",
    ref_1.default,
    type_1.default,
    enum_1.default,
    elements_1.default,
    properties_1.default,
    optionalProperties_1.default,
    discriminator_1.default,
    values_1.default,
    union_1.default,
    metadata_1.default,
    { keyword: "additionalProperties", schemaType: "boolean" },
    { keyword: "nullable", schemaType: "boolean" },
];
exports.default = jtdVocabulary;
//# sourceMappingURL=index.js.map