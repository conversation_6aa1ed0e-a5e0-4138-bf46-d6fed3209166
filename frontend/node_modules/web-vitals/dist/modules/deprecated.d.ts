export { 
/**
 * @deprecated Use `onCLS()` instead.
 */
onCLS as getCLS, } from './onCLS.js';
export { 
/**
 * @deprecated Use `onFCP()` instead.
 */
onFCP as getFCP, } from './onFCP.js';
export { 
/**
 * @deprecated Use `onFID()` instead.
 */
onFID as getFID, } from './onFID.js';
export { 
/**
 * @deprecated Use `onINP()` instead.
 */
onINP as getINP, } from './onINP.js';
export { 
/**
 * @deprecated Use `onLCP()` instead.
 */
onLCP as getLCP, } from './onLCP.js';
export { 
/**
 * @deprecated Use `onTTFB()` instead.
 */
onTTFB as getTTFB, } from './onTTFB.js';
export { 
/**
 * @deprecated Use `ReportCallback` instead.
 */
ReportCallback as ReportHandler, } from './types.js';
