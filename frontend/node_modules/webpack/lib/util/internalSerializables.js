/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/

"use strict";

// We need to include a list of requires here
// to allow webpack to be bundled with only static requires
// We could use a dynamic require(`../${request}`) but this
// would include too many modules and not every tool is able
// to process this
module.exports = {
	AsyncDependenciesBlock: () => require("../AsyncDependenciesBlock"),
	CommentCompilationWarning: () => require("../CommentCompilationWarning"),
	ContextModule: () => require("../ContextModule"),
	"cache/PackFileCacheStrategy": () =>
		require("../cache/PackFileCacheStrategy"),
	"cache/ResolverCachePlugin": () => require("../cache/ResolverCachePlugin"),
	"container/ContainerEntryDependency": () =>
		require("../container/ContainerEntryDependency"),
	"container/ContainerEntryModule": () =>
		require("../container/ContainerEntryModule"),
	"container/ContainerExposedDependency": () =>
		require("../container/ContainerExposedDependency"),
	"container/FallbackDependency": () =>
		require("../container/FallbackDependency"),
	"container/FallbackItemDependency": () =>
		require("../container/FallbackItemDependency"),
	"container/FallbackModule": () => require("../container/FallbackModule"),
	"container/RemoteModule": () => require("../container/RemoteModule"),
	"container/RemoteToExternalDependency": () =>
		require("../container/RemoteToExternalDependency"),
	"dependencies/AMDDefineDependency": () =>
		require("../dependencies/AMDDefineDependency"),
	"dependencies/AMDRequireArrayDependency": () =>
		require("../dependencies/AMDRequireArrayDependency"),
	"dependencies/AMDRequireContextDependency": () =>
		require("../dependencies/AMDRequireContextDependency"),
	"dependencies/AMDRequireDependenciesBlock": () =>
		require("../dependencies/AMDRequireDependenciesBlock"),
	"dependencies/AMDRequireDependency": () =>
		require("../dependencies/AMDRequireDependency"),
	"dependencies/AMDRequireItemDependency": () =>
		require("../dependencies/AMDRequireItemDependency"),
	"dependencies/CachedConstDependency": () =>
		require("../dependencies/CachedConstDependency"),
	"dependencies/ExternalModuleDependency": () =>
		require("../dependencies/ExternalModuleDependency"),
	"dependencies/ExternalModuleInitFragment": () =>
		require("../dependencies/ExternalModuleInitFragment"),
	"dependencies/CreateScriptUrlDependency": () =>
		require("../dependencies/CreateScriptUrlDependency"),
	"dependencies/CommonJsRequireContextDependency": () =>
		require("../dependencies/CommonJsRequireContextDependency"),
	"dependencies/CommonJsExportRequireDependency": () =>
		require("../dependencies/CommonJsExportRequireDependency"),
	"dependencies/CommonJsExportsDependency": () =>
		require("../dependencies/CommonJsExportsDependency"),
	"dependencies/CommonJsFullRequireDependency": () =>
		require("../dependencies/CommonJsFullRequireDependency"),
	"dependencies/CommonJsRequireDependency": () =>
		require("../dependencies/CommonJsRequireDependency"),
	"dependencies/CommonJsSelfReferenceDependency": () =>
		require("../dependencies/CommonJsSelfReferenceDependency"),
	"dependencies/ConstDependency": () =>
		require("../dependencies/ConstDependency"),
	"dependencies/ContextDependency": () =>
		require("../dependencies/ContextDependency"),
	"dependencies/ContextElementDependency": () =>
		require("../dependencies/ContextElementDependency"),
	"dependencies/CriticalDependencyWarning": () =>
		require("../dependencies/CriticalDependencyWarning"),
	"dependencies/CssImportDependency": () =>
		require("../dependencies/CssImportDependency"),
	"dependencies/CssLocalIdentifierDependency": () =>
		require("../dependencies/CssLocalIdentifierDependency"),
	"dependencies/CssSelfLocalIdentifierDependency": () =>
		require("../dependencies/CssSelfLocalIdentifierDependency"),
	"dependencies/CssIcssImportDependency": () =>
		require("../dependencies/CssIcssImportDependency"),
	"dependencies/CssIcssExportDependency": () =>
		require("../dependencies/CssIcssExportDependency"),
	"dependencies/CssUrlDependency": () =>
		require("../dependencies/CssUrlDependency"),
	"dependencies/CssIcssSymbolDependency": () =>
		require("../dependencies/CssIcssSymbolDependency"),
	"dependencies/DelegatedSourceDependency": () =>
		require("../dependencies/DelegatedSourceDependency"),
	"dependencies/DllEntryDependency": () =>
		require("../dependencies/DllEntryDependency"),
	"dependencies/EntryDependency": () =>
		require("../dependencies/EntryDependency"),
	"dependencies/ExportsInfoDependency": () =>
		require("../dependencies/ExportsInfoDependency"),
	"dependencies/HarmonyAcceptDependency": () =>
		require("../dependencies/HarmonyAcceptDependency"),
	"dependencies/HarmonyAcceptImportDependency": () =>
		require("../dependencies/HarmonyAcceptImportDependency"),
	"dependencies/HarmonyCompatibilityDependency": () =>
		require("../dependencies/HarmonyCompatibilityDependency"),
	"dependencies/HarmonyExportExpressionDependency": () =>
		require("../dependencies/HarmonyExportExpressionDependency"),
	"dependencies/HarmonyExportHeaderDependency": () =>
		require("../dependencies/HarmonyExportHeaderDependency"),
	"dependencies/HarmonyExportImportedSpecifierDependency": () =>
		require("../dependencies/HarmonyExportImportedSpecifierDependency"),
	"dependencies/HarmonyExportSpecifierDependency": () =>
		require("../dependencies/HarmonyExportSpecifierDependency"),
	"dependencies/HarmonyImportSideEffectDependency": () =>
		require("../dependencies/HarmonyImportSideEffectDependency"),
	"dependencies/HarmonyImportSpecifierDependency": () =>
		require("../dependencies/HarmonyImportSpecifierDependency"),
	"dependencies/HarmonyEvaluatedImportSpecifierDependency": () =>
		require("../dependencies/HarmonyEvaluatedImportSpecifierDependency"),
	"dependencies/ImportContextDependency": () =>
		require("../dependencies/ImportContextDependency"),
	"dependencies/ImportDependency": () =>
		require("../dependencies/ImportDependency"),
	"dependencies/ImportEagerDependency": () =>
		require("../dependencies/ImportEagerDependency"),
	"dependencies/ImportWeakDependency": () =>
		require("../dependencies/ImportWeakDependency"),
	"dependencies/JsonExportsDependency": () =>
		require("../dependencies/JsonExportsDependency"),
	"dependencies/LocalModule": () => require("../dependencies/LocalModule"),
	"dependencies/LocalModuleDependency": () =>
		require("../dependencies/LocalModuleDependency"),
	"dependencies/ModuleDecoratorDependency": () =>
		require("../dependencies/ModuleDecoratorDependency"),
	"dependencies/ModuleHotAcceptDependency": () =>
		require("../dependencies/ModuleHotAcceptDependency"),
	"dependencies/ModuleHotDeclineDependency": () =>
		require("../dependencies/ModuleHotDeclineDependency"),
	"dependencies/ImportMetaHotAcceptDependency": () =>
		require("../dependencies/ImportMetaHotAcceptDependency"),
	"dependencies/ImportMetaHotDeclineDependency": () =>
		require("../dependencies/ImportMetaHotDeclineDependency"),
	"dependencies/ImportMetaContextDependency": () =>
		require("../dependencies/ImportMetaContextDependency"),
	"dependencies/ProvidedDependency": () =>
		require("../dependencies/ProvidedDependency"),
	"dependencies/PureExpressionDependency": () =>
		require("../dependencies/PureExpressionDependency"),
	"dependencies/RequireContextDependency": () =>
		require("../dependencies/RequireContextDependency"),
	"dependencies/RequireEnsureDependenciesBlock": () =>
		require("../dependencies/RequireEnsureDependenciesBlock"),
	"dependencies/RequireEnsureDependency": () =>
		require("../dependencies/RequireEnsureDependency"),
	"dependencies/RequireEnsureItemDependency": () =>
		require("../dependencies/RequireEnsureItemDependency"),
	"dependencies/RequireHeaderDependency": () =>
		require("../dependencies/RequireHeaderDependency"),
	"dependencies/RequireIncludeDependency": () =>
		require("../dependencies/RequireIncludeDependency"),
	"dependencies/RequireIncludeDependencyParserPlugin": () =>
		require("../dependencies/RequireIncludeDependencyParserPlugin"),
	"dependencies/RequireResolveContextDependency": () =>
		require("../dependencies/RequireResolveContextDependency"),
	"dependencies/RequireResolveDependency": () =>
		require("../dependencies/RequireResolveDependency"),
	"dependencies/RequireResolveHeaderDependency": () =>
		require("../dependencies/RequireResolveHeaderDependency"),
	"dependencies/RuntimeRequirementsDependency": () =>
		require("../dependencies/RuntimeRequirementsDependency"),
	"dependencies/StaticExportsDependency": () =>
		require("../dependencies/StaticExportsDependency"),
	"dependencies/SystemPlugin": () => require("../dependencies/SystemPlugin"),
	"dependencies/UnsupportedDependency": () =>
		require("../dependencies/UnsupportedDependency"),
	"dependencies/URLDependency": () => require("../dependencies/URLDependency"),
	"dependencies/WebAssemblyExportImportedDependency": () =>
		require("../dependencies/WebAssemblyExportImportedDependency"),
	"dependencies/WebAssemblyImportDependency": () =>
		require("../dependencies/WebAssemblyImportDependency"),
	"dependencies/WebpackIsIncludedDependency": () =>
		require("../dependencies/WebpackIsIncludedDependency"),
	"dependencies/WorkerDependency": () =>
		require("../dependencies/WorkerDependency"),
	"json/JsonData": () => require("../json/JsonData"),
	"optimize/ConcatenatedModule": () =>
		require("../optimize/ConcatenatedModule"),
	DelegatedModule: () => require("../DelegatedModule"),
	DependenciesBlock: () => require("../DependenciesBlock"),
	DllModule: () => require("../DllModule"),
	ExternalModule: () => require("../ExternalModule"),
	FileSystemInfo: () => require("../FileSystemInfo"),
	InitFragment: () => require("../InitFragment"),
	InvalidDependenciesModuleWarning: () =>
		require("../InvalidDependenciesModuleWarning"),
	Module: () => require("../Module"),
	ModuleBuildError: () => require("../ModuleBuildError"),
	ModuleDependencyWarning: () => require("../ModuleDependencyWarning"),
	ModuleError: () => require("../ModuleError"),
	ModuleGraph: () => require("../ModuleGraph"),
	ModuleParseError: () => require("../ModuleParseError"),
	ModuleWarning: () => require("../ModuleWarning"),
	NormalModule: () => require("../NormalModule"),
	CssModule: () => require("../CssModule"),
	RawDataUrlModule: () => require("../asset/RawDataUrlModule"),
	RawModule: () => require("../RawModule"),
	"sharing/ConsumeSharedModule": () =>
		require("../sharing/ConsumeSharedModule"),
	"sharing/ConsumeSharedFallbackDependency": () =>
		require("../sharing/ConsumeSharedFallbackDependency"),
	"sharing/ProvideSharedModule": () =>
		require("../sharing/ProvideSharedModule"),
	"sharing/ProvideSharedDependency": () =>
		require("../sharing/ProvideSharedDependency"),
	"sharing/ProvideForSharedDependency": () =>
		require("../sharing/ProvideForSharedDependency"),
	UnsupportedFeatureWarning: () => require("../UnsupportedFeatureWarning"),
	"util/LazySet": () => require("../util/LazySet"),
	UnhandledSchemeError: () => require("../UnhandledSchemeError"),
	NodeStuffInWebError: () => require("../NodeStuffInWebError"),
	EnvironmentNotSupportAsyncWarning: () =>
		require("../EnvironmentNotSupportAsyncWarning"),
	WebpackError: () => require("../WebpackError"),

	"util/registerExternalSerializer": () => {
		// already registered
	}
};
