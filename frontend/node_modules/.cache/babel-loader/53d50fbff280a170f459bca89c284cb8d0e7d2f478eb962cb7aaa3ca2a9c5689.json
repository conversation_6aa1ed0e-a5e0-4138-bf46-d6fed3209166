{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/demo/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './App.css';\nimport ScrapingForm from './components/ScrapingForm';\nimport DataDisplay from './components/DataDisplay';\nimport PDFGenerator from './components/PDFGenerator';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [scrapedData, setScrapedData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const handleDataScraped = newData => {\n    setScrapedData(prev => [...prev, newData]);\n  };\n  const clearData = () => {\n    setScrapedData([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"App-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDE80 Social Media Scraper & PDF Generator\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Enter TikTok and Instagram URLs to scrape performance data and generate PDF reports\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"App-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"scraping-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDCCA Data Scraping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ScrapingForm, {\n          onDataScraped: handleDataScraped,\n          loading: loading,\n          setLoading: setLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), scrapedData.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"data-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [\"\\uD83D\\uDCC8 Scraped Data (\", scrapedData.length, \" items)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"clear-btn\",\n            onClick: clearData,\n            title: \"Clear all data\",\n            children: \"\\uD83D\\uDDD1\\uFE0F Clear\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DataDisplay, {\n          data: scrapedData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"pdf-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDCC4 PDF Generator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PDFGenerator, {\n          scrapedData: scrapedData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"instructions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDCDD How to Use\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"instructions-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"1. Scrape Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Enter TikTok or Instagram URLs to extract performance metrics like views, likes, and comments.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"2. View Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"See the scraped data displayed in organized cards with all the extracted information.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instruction-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"3. Generate PDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Use the scraped data or custom JSON to create professional PDF reports for download.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"App-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Built with React.js & Node.js | Powered by Puppeteer & PDFKit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"T0kzRZ8I4uHrA9LDweg+GL7DuNk=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "ScrapingForm", "DataDisplay", "PDFGenerator", "jsxDEV", "_jsxDEV", "App", "_s", "scrapedData", "setScrapedData", "loading", "setLoading", "handleDataScraped", "newData", "prev", "clearData", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDataScraped", "length", "onClick", "title", "data", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/demo/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport './App.css';\nimport ScrapingForm from './components/ScrapingForm';\nimport DataDisplay from './components/DataDisplay';\nimport PDFGenerator from './components/PDFGenerator';\n\nfunction App() {\n  const [scrapedData, setScrapedData] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  const handleDataScraped = (newData) => {\n    setScrapedData(prev => [...prev, newData]);\n  };\n\n  const clearData = () => {\n    setScrapedData([]);\n  };\n\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>🚀 Social Media Scraper & PDF Generator</h1>\n        <p>Enter TikTok and Instagram URLs to scrape performance data and generate PDF reports</p>\n      </header>\n\n      <main className=\"App-main\">\n        {/* Scraping Section */}\n        <section className=\"scraping-section\">\n          <h2>📊 Data Scraping</h2>\n          <ScrapingForm \n            onDataScraped={handleDataScraped}\n            loading={loading}\n            setLoading={setLoading}\n          />\n        </section>\n\n        {/* Data Display Section */}\n        {scrapedData.length > 0 && (\n          <section className=\"data-section\">\n            <div className=\"section-header\">\n              <h2>📈 Scraped Data ({scrapedData.length} items)</h2>\n              <button \n                className=\"clear-btn\"\n                onClick={clearData}\n                title=\"Clear all data\"\n              >\n                🗑️ Clear\n              </button>\n            </div>\n            <DataDisplay data={scrapedData} />\n          </section>\n        )}\n\n        {/* PDF Generation Section */}\n        <section className=\"pdf-section\">\n          <h2>📄 PDF Generator</h2>\n          <PDFGenerator scrapedData={scrapedData} />\n        </section>\n\n        {/* Instructions */}\n        <section className=\"instructions\">\n          <h3>📝 How to Use</h3>\n          <div className=\"instructions-grid\">\n            <div className=\"instruction-card\">\n              <h4>1. Scrape Data</h4>\n              <p>Enter TikTok or Instagram URLs to extract performance metrics like views, likes, and comments.</p>\n            </div>\n            <div className=\"instruction-card\">\n              <h4>2. View Results</h4>\n              <p>See the scraped data displayed in organized cards with all the extracted information.</p>\n            </div>\n            <div className=\"instruction-card\">\n              <h4>3. Generate PDF</h4>\n              <p>Use the scraped data or custom JSON to create professional PDF reports for download.</p>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      <footer className=\"App-footer\">\n        <p>Built with React.js & Node.js | Powered by Puppeteer & PDFKit</p>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,WAAW;AAClB,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMY,iBAAiB,GAAIC,OAAO,IAAK;IACrCJ,cAAc,CAACK,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,OAAO,CAAC,CAAC;EAC5C,CAAC;EAED,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtBN,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,oBACEJ,OAAA;IAAKW,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBZ,OAAA;MAAQW,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC5BZ,OAAA;QAAAY,QAAA,EAAI;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChDhB,OAAA;QAAAY,QAAA,EAAG;MAAmF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC,eAEThB,OAAA;MAAMW,SAAS,EAAC,UAAU;MAAAC,QAAA,gBAExBZ,OAAA;QAASW,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACnCZ,OAAA;UAAAY,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBhB,OAAA,CAACJ,YAAY;UACXqB,aAAa,EAAEV,iBAAkB;UACjCF,OAAO,EAAEA,OAAQ;UACjBC,UAAU,EAAEA;QAAW;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAGTb,WAAW,CAACe,MAAM,GAAG,CAAC,iBACrBlB,OAAA;QAASW,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC/BZ,OAAA;UAAKW,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BZ,OAAA;YAAAY,QAAA,GAAI,6BAAiB,EAACT,WAAW,CAACe,MAAM,EAAC,SAAO;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDhB,OAAA;YACEW,SAAS,EAAC,WAAW;YACrBQ,OAAO,EAAET,SAAU;YACnBU,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EACvB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNhB,OAAA,CAACH,WAAW;UAACwB,IAAI,EAAElB;QAAY;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACV,eAGDhB,OAAA;QAASW,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC9BZ,OAAA;UAAAY,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBhB,OAAA,CAACF,YAAY;UAACK,WAAW,EAAEA;QAAY;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eAGVhB,OAAA;QAASW,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC/BZ,OAAA;UAAAY,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBhB,OAAA;UAAKW,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCZ,OAAA;YAAKW,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BZ,OAAA;cAAAY,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBhB,OAAA;cAAAY,QAAA,EAAG;YAA8F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNhB,OAAA;YAAKW,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BZ,OAAA;cAAAY,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBhB,OAAA;cAAAY,QAAA,EAAG;YAAqF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eACNhB,OAAA;YAAKW,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BZ,OAAA;cAAAY,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBhB,OAAA;cAAAY,QAAA,EAAG;YAAoF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEPhB,OAAA;MAAQW,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BZ,OAAA;QAAAY,QAAA,EAAG;MAA6D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACd,EAAA,CA9EQD,GAAG;AAAAqB,EAAA,GAAHrB,GAAG;AAgFZ,eAAeA,GAAG;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}