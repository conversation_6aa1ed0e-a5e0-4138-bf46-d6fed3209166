{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/demo/frontend/src/components/PDFGenerator.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PDFGenerator = ({\n  scrapedData\n}) => {\n  _s();\n  const [customJson, setCustomJson] = useState('');\n  const [pdfTitle, setPdfTitle] = useState('Social Media Report');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [useScrapedData, setUseScrapedData] = useState(true);\n\n  // Sample JSON for demonstration\n  const sampleJson = {\n    title: \"Social Media Performance Report\",\n    date: new Date().toISOString().split('T')[0],\n    summary: {\n      totalViews: 125000,\n      totalLikes: 8500,\n      totalComments: 450,\n      totalShares: 320\n    },\n    platforms: [{\n      name: \"TikTok\",\n      url: \"https://tiktok.com/@username/video/123456789\",\n      metrics: {\n        views: 75000,\n        likes: 5200,\n        comments: 280,\n        shares: 190\n      },\n      description: \"Viral dance video that gained massive traction\"\n    }, {\n      name: \"Instagram\",\n      url: \"https://instagram.com/p/ABC123DEF456/\",\n      metrics: {\n        views: 50000,\n        likes: 3300,\n        comments: 170,\n        shares: 130\n      },\n      description: \"Behind-the-scenes content from latest photoshoot\"\n    }],\n    insights: [\"TikTok content performed 50% better than Instagram\", \"Dance videos generate highest engagement\", \"Peak engagement time: 7-9 PM EST\"]\n  };\n  const convertScrapedDataToPdfFormat = data => {\n    const platforms = data.map(item => ({\n      name: item.platform,\n      url: item.url,\n      metrics: {\n        views: item.data.views || 'N/A',\n        likes: item.data.likes || 'N/A',\n        comments: item.data.comments || 'N/A',\n        shares: item.data.shares || 'N/A'\n      },\n      description: item.data.title || item.data.description || 'Social media content'\n    }));\n\n    // Calculate totals (only for numeric values)\n    const totals = platforms.reduce((acc, platform) => {\n      const parseMetric = value => {\n        if (!value || value === 'N/A') return 0;\n        const str = value.toString().toLowerCase();\n        if (str.includes('k')) return parseFloat(str) * 1000;\n        if (str.includes('m')) return parseFloat(str) * 1000000;\n        return parseInt(str) || 0;\n      };\n      acc.totalViews += parseMetric(platform.metrics.views);\n      acc.totalLikes += parseMetric(platform.metrics.likes);\n      acc.totalComments += parseMetric(platform.metrics.comments);\n      acc.totalShares += parseMetric(platform.metrics.shares);\n      return acc;\n    }, {\n      totalViews: 0,\n      totalLikes: 0,\n      totalComments: 0,\n      totalShares: 0\n    });\n    return {\n      title: pdfTitle,\n      date: new Date().toISOString().split('T')[0],\n      summary: totals,\n      platforms: platforms,\n      insights: [`Analyzed ${platforms.length} social media posts`, `Data scraped on ${new Date().toLocaleDateString()}`, \"Metrics may vary due to platform privacy settings\"]\n    };\n  };\n  const generatePDF = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      let dataToSend;\n      if (useScrapedData && scrapedData.length > 0) {\n        dataToSend = convertScrapedDataToPdfFormat(scrapedData);\n      } else if (!useScrapedData && customJson.trim()) {\n        try {\n          dataToSend = JSON.parse(customJson);\n        } catch (parseError) {\n          setError('Invalid JSON format. Please check your JSON syntax.');\n          setLoading(false);\n          return;\n        }\n      } else {\n        setError('Please provide data for PDF generation (either scraped data or custom JSON).');\n        setLoading(false);\n        return;\n      }\n      const response = await axios.post('http://localhost:5000/api/pdf/generate', {\n        data: dataToSend,\n        title: pdfTitle\n      }, {\n        responseType: 'blob'\n      });\n\n      // Create download link\n      const blob = new Blob([response.data], {\n        type: 'application/pdf'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${pdfTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('PDF generation error:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Failed to generate PDF');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadSampleJson = () => {\n    setCustomJson(JSON.stringify(sampleJson, null, 2));\n    setUseScrapedData(false);\n  };\n  const getSampleFromAPI = async () => {\n    try {\n      const response = await axios.get('http://localhost:5000/api/pdf/sample');\n      setCustomJson(JSON.stringify(response.data.data, null, 2));\n      setUseScrapedData(false);\n    } catch (error) {\n      console.error('Failed to get sample:', error);\n      loadSampleJson(); // Fallback to local sample\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pdf-generator\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pdf-title-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"pdfTitle\",\n        children: \"\\uD83D\\uDCC4 PDF Title:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: \"pdfTitle\",\n        type: \"text\",\n        value: pdfTitle,\n        onChange: e => setPdfTitle(e.target.value),\n        placeholder: \"Enter PDF title\",\n        className: \"pdf-title-input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"data-source-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"\\uD83D\\uDCCA Data Source:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"radio-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"radio-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            checked: useScrapedData,\n            onChange: () => setUseScrapedData(true),\n            disabled: scrapedData.length === 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Use Scraped Data (\", scrapedData.length, \" items)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), scrapedData.length === 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"disabled-note\",\n            children: \"No scraped data available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 42\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"radio-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            checked: !useScrapedData,\n            onChange: () => setUseScrapedData(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Use Custom JSON\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), !useScrapedData && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"custom-json-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"json-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"\\uD83D\\uDCDD Custom JSON Data:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"json-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: loadSampleJson,\n            className: \"sample-btn\",\n            children: \"\\uD83D\\uDCCB Load Sample\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: getSampleFromAPI,\n            className: \"sample-btn\",\n            children: \"\\uD83C\\uDF10 Get API Sample\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: customJson,\n        onChange: e => setCustomJson(e.target.value),\n        placeholder: \"Enter your JSON data here...\",\n        className: \"json-textarea\",\n        rows: 15\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"generate-section\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: generatePDF,\n        disabled: loading || useScrapedData && scrapedData.length === 0 || !useScrapedData && !customJson.trim(),\n        className: \"generate-btn\",\n        children: loading ? '⏳ Generating PDF...' : '📄 Generate & Download PDF'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [\"\\u274C \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this), useScrapedData && scrapedData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"\\uD83D\\uDC40 Preview (Scraped Data):\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        className: \"preview-content\",\n        children: JSON.stringify(convertScrapedDataToPdfFormat(scrapedData), null, 2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n};\n_s(PDFGenerator, \"jlzxpUVuAcj/WpsJPjWaCLM8xMM=\");\n_c = PDFGenerator;\nexport default PDFGenerator;\nvar _c;\n$RefreshReg$(_c, \"PDFGenerator\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "PDFGenerator", "scrapedData", "_s", "customJson", "set<PERSON>ust<PERSON><PERSON>son", "pdfTitle", "setPdfTitle", "loading", "setLoading", "error", "setError", "useScrapedData", "setUseScrapedData", "sampleJson", "title", "date", "Date", "toISOString", "split", "summary", "totalViews", "totalLikes", "totalComments", "totalShares", "platforms", "name", "url", "metrics", "views", "likes", "comments", "shares", "description", "insights", "convertScrapedDataToPdfFormat", "data", "map", "item", "platform", "totals", "reduce", "acc", "parseMetric", "value", "str", "toString", "toLowerCase", "includes", "parseFloat", "parseInt", "length", "toLocaleDateString", "generatePDF", "dataToSend", "trim", "JSON", "parse", "parseError", "response", "post", "responseType", "blob", "Blob", "type", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_error$response", "_error$response$data", "console", "loadSampleJson", "stringify", "getSampleFromAPI", "get", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "onChange", "e", "target", "placeholder", "checked", "disabled", "onClick", "rows", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/demo/frontend/src/components/PDFGenerator.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\n\nconst PDFGenerator = ({ scrapedData }) => {\n  const [customJson, setCustomJson] = useState('');\n  const [pdfTitle, setPdfTitle] = useState('Social Media Report');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [useScrapedData, setUseScrapedData] = useState(true);\n\n  // Sample JSON for demonstration\n  const sampleJson = {\n    title: \"Social Media Performance Report\",\n    date: new Date().toISOString().split('T')[0],\n    summary: {\n      totalViews: 125000,\n      totalLikes: 8500,\n      totalComments: 450,\n      totalShares: 320\n    },\n    platforms: [\n      {\n        name: \"TikTok\",\n        url: \"https://tiktok.com/@username/video/123456789\",\n        metrics: {\n          views: 75000,\n          likes: 5200,\n          comments: 280,\n          shares: 190\n        },\n        description: \"Viral dance video that gained massive traction\"\n      },\n      {\n        name: \"Instagram\",\n        url: \"https://instagram.com/p/ABC123DEF456/\",\n        metrics: {\n          views: 50000,\n          likes: 3300,\n          comments: 170,\n          shares: 130\n        },\n        description: \"Behind-the-scenes content from latest photoshoot\"\n      }\n    ],\n    insights: [\n      \"TikTok content performed 50% better than Instagram\",\n      \"Dance videos generate highest engagement\",\n      \"Peak engagement time: 7-9 PM EST\"\n    ]\n  };\n\n  const convertScrapedDataToPdfFormat = (data) => {\n    const platforms = data.map(item => ({\n      name: item.platform,\n      url: item.url,\n      metrics: {\n        views: item.data.views || 'N/A',\n        likes: item.data.likes || 'N/A',\n        comments: item.data.comments || 'N/A',\n        shares: item.data.shares || 'N/A'\n      },\n      description: item.data.title || item.data.description || 'Social media content'\n    }));\n\n    // Calculate totals (only for numeric values)\n    const totals = platforms.reduce((acc, platform) => {\n      const parseMetric = (value) => {\n        if (!value || value === 'N/A') return 0;\n        const str = value.toString().toLowerCase();\n        if (str.includes('k')) return parseFloat(str) * 1000;\n        if (str.includes('m')) return parseFloat(str) * 1000000;\n        return parseInt(str) || 0;\n      };\n\n      acc.totalViews += parseMetric(platform.metrics.views);\n      acc.totalLikes += parseMetric(platform.metrics.likes);\n      acc.totalComments += parseMetric(platform.metrics.comments);\n      acc.totalShares += parseMetric(platform.metrics.shares);\n      return acc;\n    }, { totalViews: 0, totalLikes: 0, totalComments: 0, totalShares: 0 });\n\n    return {\n      title: pdfTitle,\n      date: new Date().toISOString().split('T')[0],\n      summary: totals,\n      platforms: platforms,\n      insights: [\n        `Analyzed ${platforms.length} social media posts`,\n        `Data scraped on ${new Date().toLocaleDateString()}`,\n        \"Metrics may vary due to platform privacy settings\"\n      ]\n    };\n  };\n\n  const generatePDF = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      let dataToSend;\n\n      if (useScrapedData && scrapedData.length > 0) {\n        dataToSend = convertScrapedDataToPdfFormat(scrapedData);\n      } else if (!useScrapedData && customJson.trim()) {\n        try {\n          dataToSend = JSON.parse(customJson);\n        } catch (parseError) {\n          setError('Invalid JSON format. Please check your JSON syntax.');\n          setLoading(false);\n          return;\n        }\n      } else {\n        setError('Please provide data for PDF generation (either scraped data or custom JSON).');\n        setLoading(false);\n        return;\n      }\n\n      const response = await axios.post(\n        'http://localhost:5000/api/pdf/generate',\n        { data: dataToSend, title: pdfTitle },\n        { responseType: 'blob' }\n      );\n\n      // Create download link\n      const blob = new Blob([response.data], { type: 'application/pdf' });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${pdfTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n\n    } catch (error) {\n      console.error('PDF generation error:', error);\n      setError(error.response?.data?.error || 'Failed to generate PDF');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadSampleJson = () => {\n    setCustomJson(JSON.stringify(sampleJson, null, 2));\n    setUseScrapedData(false);\n  };\n\n  const getSampleFromAPI = async () => {\n    try {\n      const response = await axios.get('http://localhost:5000/api/pdf/sample');\n      setCustomJson(JSON.stringify(response.data.data, null, 2));\n      setUseScrapedData(false);\n    } catch (error) {\n      console.error('Failed to get sample:', error);\n      loadSampleJson(); // Fallback to local sample\n    }\n  };\n\n  return (\n    <div className=\"pdf-generator\">\n      {/* PDF Title Input */}\n      <div className=\"pdf-title-section\">\n        <label htmlFor=\"pdfTitle\">📄 PDF Title:</label>\n        <input\n          id=\"pdfTitle\"\n          type=\"text\"\n          value={pdfTitle}\n          onChange={(e) => setPdfTitle(e.target.value)}\n          placeholder=\"Enter PDF title\"\n          className=\"pdf-title-input\"\n        />\n      </div>\n\n      {/* Data Source Selection */}\n      <div className=\"data-source-section\">\n        <h4>📊 Data Source:</h4>\n        <div className=\"radio-group\">\n          <label className=\"radio-option\">\n            <input\n              type=\"radio\"\n              checked={useScrapedData}\n              onChange={() => setUseScrapedData(true)}\n              disabled={scrapedData.length === 0}\n            />\n            <span>Use Scraped Data ({scrapedData.length} items)</span>\n            {scrapedData.length === 0 && <span className=\"disabled-note\">No scraped data available</span>}\n          </label>\n          \n          <label className=\"radio-option\">\n            <input\n              type=\"radio\"\n              checked={!useScrapedData}\n              onChange={() => setUseScrapedData(false)}\n            />\n            <span>Use Custom JSON</span>\n          </label>\n        </div>\n      </div>\n\n      {/* Custom JSON Section */}\n      {!useScrapedData && (\n        <div className=\"custom-json-section\">\n          <div className=\"json-header\">\n            <h4>📝 Custom JSON Data:</h4>\n            <div className=\"json-buttons\">\n              <button onClick={loadSampleJson} className=\"sample-btn\">\n                📋 Load Sample\n              </button>\n              <button onClick={getSampleFromAPI} className=\"sample-btn\">\n                🌐 Get API Sample\n              </button>\n            </div>\n          </div>\n          \n          <textarea\n            value={customJson}\n            onChange={(e) => setCustomJson(e.target.value)}\n            placeholder=\"Enter your JSON data here...\"\n            className=\"json-textarea\"\n            rows={15}\n          />\n        </div>\n      )}\n\n      {/* Generate Button */}\n      <div className=\"generate-section\">\n        <button\n          onClick={generatePDF}\n          disabled={loading || (useScrapedData && scrapedData.length === 0) || (!useScrapedData && !customJson.trim())}\n          className=\"generate-btn\"\n        >\n          {loading ? '⏳ Generating PDF...' : '📄 Generate & Download PDF'}\n        </button>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"error-message\">\n          ❌ {error}\n        </div>\n      )}\n\n      {/* Preview Section */}\n      {useScrapedData && scrapedData.length > 0 && (\n        <div className=\"preview-section\">\n          <h4>👀 Preview (Scraped Data):</h4>\n          <pre className=\"preview-content\">\n            {JSON.stringify(convertScrapedDataToPdfFormat(scrapedData), null, 2)}\n          </pre>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PDFGenerator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,qBAAqB,CAAC;EAC/D,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAMiB,UAAU,GAAG;IACjBC,KAAK,EAAE,iCAAiC;IACxCC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,OAAO,EAAE;MACPC,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,GAAG;MAClBC,WAAW,EAAE;IACf,CAAC;IACDC,SAAS,EAAE,CACT;MACEC,IAAI,EAAE,QAAQ;MACdC,GAAG,EAAE,8CAA8C;MACnDC,OAAO,EAAE;QACPC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE;MACV,CAAC;MACDC,WAAW,EAAE;IACf,CAAC,EACD;MACEP,IAAI,EAAE,WAAW;MACjBC,GAAG,EAAE,uCAAuC;MAC5CC,OAAO,EAAE;QACPC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE;MACV,CAAC;MACDC,WAAW,EAAE;IACf,CAAC,CACF;IACDC,QAAQ,EAAE,CACR,oDAAoD,EACpD,0CAA0C,EAC1C,kCAAkC;EAEtC,CAAC;EAED,MAAMC,6BAA6B,GAAIC,IAAI,IAAK;IAC9C,MAAMX,SAAS,GAAGW,IAAI,CAACC,GAAG,CAACC,IAAI,KAAK;MAClCZ,IAAI,EAAEY,IAAI,CAACC,QAAQ;MACnBZ,GAAG,EAAEW,IAAI,CAACX,GAAG;MACbC,OAAO,EAAE;QACPC,KAAK,EAAES,IAAI,CAACF,IAAI,CAACP,KAAK,IAAI,KAAK;QAC/BC,KAAK,EAAEQ,IAAI,CAACF,IAAI,CAACN,KAAK,IAAI,KAAK;QAC/BC,QAAQ,EAAEO,IAAI,CAACF,IAAI,CAACL,QAAQ,IAAI,KAAK;QACrCC,MAAM,EAAEM,IAAI,CAACF,IAAI,CAACJ,MAAM,IAAI;MAC9B,CAAC;MACDC,WAAW,EAAEK,IAAI,CAACF,IAAI,CAACrB,KAAK,IAAIuB,IAAI,CAACF,IAAI,CAACH,WAAW,IAAI;IAC3D,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMO,MAAM,GAAGf,SAAS,CAACgB,MAAM,CAAC,CAACC,GAAG,EAAEH,QAAQ,KAAK;MACjD,MAAMI,WAAW,GAAIC,KAAK,IAAK;QAC7B,IAAI,CAACA,KAAK,IAAIA,KAAK,KAAK,KAAK,EAAE,OAAO,CAAC;QACvC,MAAMC,GAAG,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC1C,IAAIF,GAAG,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAOC,UAAU,CAACJ,GAAG,CAAC,GAAG,IAAI;QACpD,IAAIA,GAAG,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAOC,UAAU,CAACJ,GAAG,CAAC,GAAG,OAAO;QACvD,OAAOK,QAAQ,CAACL,GAAG,CAAC,IAAI,CAAC;MAC3B,CAAC;MAEDH,GAAG,CAACrB,UAAU,IAAIsB,WAAW,CAACJ,QAAQ,CAACX,OAAO,CAACC,KAAK,CAAC;MACrDa,GAAG,CAACpB,UAAU,IAAIqB,WAAW,CAACJ,QAAQ,CAACX,OAAO,CAACE,KAAK,CAAC;MACrDY,GAAG,CAACnB,aAAa,IAAIoB,WAAW,CAACJ,QAAQ,CAACX,OAAO,CAACG,QAAQ,CAAC;MAC3DW,GAAG,CAAClB,WAAW,IAAImB,WAAW,CAACJ,QAAQ,CAACX,OAAO,CAACI,MAAM,CAAC;MACvD,OAAOU,GAAG;IACZ,CAAC,EAAE;MAAErB,UAAU,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,aAAa,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,CAAC;IAEtE,OAAO;MACLT,KAAK,EAAET,QAAQ;MACfU,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CC,OAAO,EAAEoB,MAAM;MACff,SAAS,EAAEA,SAAS;MACpBS,QAAQ,EAAE,CACR,YAAYT,SAAS,CAAC0B,MAAM,qBAAqB,EACjD,mBAAmB,IAAIlC,IAAI,CAAC,CAAC,CAACmC,kBAAkB,CAAC,CAAC,EAAE,EACpD,mDAAmD;IAEvD,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B5C,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,IAAI2C,UAAU;MAEd,IAAI1C,cAAc,IAAIV,WAAW,CAACiD,MAAM,GAAG,CAAC,EAAE;QAC5CG,UAAU,GAAGnB,6BAA6B,CAACjC,WAAW,CAAC;MACzD,CAAC,MAAM,IAAI,CAACU,cAAc,IAAIR,UAAU,CAACmD,IAAI,CAAC,CAAC,EAAE;QAC/C,IAAI;UACFD,UAAU,GAAGE,IAAI,CAACC,KAAK,CAACrD,UAAU,CAAC;QACrC,CAAC,CAAC,OAAOsD,UAAU,EAAE;UACnB/C,QAAQ,CAAC,qDAAqD,CAAC;UAC/DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,MAAM;QACLE,QAAQ,CAAC,8EAA8E,CAAC;QACxFF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAMkD,QAAQ,GAAG,MAAM7D,KAAK,CAAC8D,IAAI,CAC/B,wCAAwC,EACxC;QAAExB,IAAI,EAAEkB,UAAU;QAAEvC,KAAK,EAAET;MAAS,CAAC,EACrC;QAAEuD,YAAY,EAAE;MAAO,CACzB,CAAC;;MAED;MACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,QAAQ,CAACvB,IAAI,CAAC,EAAE;QAAE4B,IAAI,EAAE;MAAkB,CAAC,CAAC;MACnE,MAAMrC,GAAG,GAAGsC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG5C,GAAG;MACfyC,IAAI,CAACI,QAAQ,GAAG,GAAGlE,QAAQ,CAACmE,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC1B,WAAW,CAAC,CAAC,MAAM;MAC3EsB,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACY,eAAe,CAACnD,GAAG,CAAC;IAEjC,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAAqE,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACvE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,EAAAoE,eAAA,GAAArE,KAAK,CAACiD,QAAQ,cAAAoB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB3C,IAAI,cAAA4C,oBAAA,uBAApBA,oBAAA,CAAsBtE,KAAK,KAAI,wBAAwB,CAAC;IACnE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyE,cAAc,GAAGA,CAAA,KAAM;IAC3B7E,aAAa,CAACmD,IAAI,CAAC2B,SAAS,CAACrE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAClDD,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMuE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAM7D,KAAK,CAACuF,GAAG,CAAC,sCAAsC,CAAC;MACxEhF,aAAa,CAACmD,IAAI,CAAC2B,SAAS,CAACxB,QAAQ,CAACvB,IAAI,CAACA,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAC1DvB,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAOH,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CwE,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB;EACF,CAAC;EAED,oBACElF,OAAA;IAAKsF,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BvF,OAAA;MAAKsF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvF,OAAA;QAAOwF,OAAO,EAAC,UAAU;QAAAD,QAAA,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/C5F,OAAA;QACE6F,EAAE,EAAC,UAAU;QACb7B,IAAI,EAAC,MAAM;QACXpB,KAAK,EAAEtC,QAAS;QAChBwF,QAAQ,EAAGC,CAAC,IAAKxF,WAAW,CAACwF,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;QAC7CqD,WAAW,EAAC,iBAAiB;QAC7BX,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5F,OAAA;MAAKsF,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCvF,OAAA;QAAAuF,QAAA,EAAI;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB5F,OAAA;QAAKsF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvF,OAAA;UAAOsF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BvF,OAAA;YACEgE,IAAI,EAAC,OAAO;YACZkC,OAAO,EAAEtF,cAAe;YACxBkF,QAAQ,EAAEA,CAAA,KAAMjF,iBAAiB,CAAC,IAAI,CAAE;YACxCsF,QAAQ,EAAEjG,WAAW,CAACiD,MAAM,KAAK;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACF5F,OAAA;YAAAuF,QAAA,GAAM,oBAAkB,EAACrF,WAAW,CAACiD,MAAM,EAAC,SAAO;UAAA;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACzD1F,WAAW,CAACiD,MAAM,KAAK,CAAC,iBAAInD,OAAA;YAAMsF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eAER5F,OAAA;UAAOsF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BvF,OAAA;YACEgE,IAAI,EAAC,OAAO;YACZkC,OAAO,EAAE,CAACtF,cAAe;YACzBkF,QAAQ,EAAEA,CAAA,KAAMjF,iBAAiB,CAAC,KAAK;UAAE;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACF5F,OAAA;YAAAuF,QAAA,EAAM;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAAChF,cAAc,iBACdZ,OAAA;MAAKsF,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCvF,OAAA;QAAKsF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvF,OAAA;UAAAuF,QAAA,EAAI;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7B5F,OAAA;UAAKsF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvF,OAAA;YAAQoG,OAAO,EAAElB,cAAe;YAACI,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5F,OAAA;YAAQoG,OAAO,EAAEhB,gBAAiB;YAACE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE1D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5F,OAAA;QACE4C,KAAK,EAAExC,UAAW;QAClB0F,QAAQ,EAAGC,CAAC,IAAK1F,aAAa,CAAC0F,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;QAC/CqD,WAAW,EAAC,8BAA8B;QAC1CX,SAAS,EAAC,eAAe;QACzBe,IAAI,EAAE;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGD5F,OAAA;MAAKsF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BvF,OAAA;QACEoG,OAAO,EAAE/C,WAAY;QACrB8C,QAAQ,EAAE3F,OAAO,IAAKI,cAAc,IAAIV,WAAW,CAACiD,MAAM,KAAK,CAAE,IAAK,CAACvC,cAAc,IAAI,CAACR,UAAU,CAACmD,IAAI,CAAC,CAAG;QAC7G+B,SAAS,EAAC,cAAc;QAAAC,QAAA,EAEvB/E,OAAO,GAAG,qBAAqB,GAAG;MAA4B;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLlF,KAAK,iBACJV,OAAA;MAAKsF,SAAS,EAAC,eAAe;MAAAC,QAAA,GAAC,SAC3B,EAAC7E,KAAK;IAAA;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EAGAhF,cAAc,IAAIV,WAAW,CAACiD,MAAM,GAAG,CAAC,iBACvCnD,OAAA;MAAKsF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvF,OAAA;QAAAuF,QAAA,EAAI;MAA0B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC5F,OAAA;QAAKsF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B/B,IAAI,CAAC2B,SAAS,CAAChD,6BAA6B,CAACjC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC;MAAC;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzF,EAAA,CA1PIF,YAAY;AAAAqG,EAAA,GAAZrG,YAAY;AA4PlB,eAAeA,YAAY;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}