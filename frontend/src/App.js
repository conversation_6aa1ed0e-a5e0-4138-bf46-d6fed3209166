import React, { useState } from 'react';
import './App.css';
import ScrapingForm from './components/ScrapingForm';
import DataDisplay from './components/DataDisplay';
import PDFGenerator from './components/PDFGenerator';

function App() {
  const [scrapedData, setScrapedData] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleDataScraped = (newData) => {
    setScrapedData(prev => [...prev, newData]);
  };

  const clearData = () => {
    setScrapedData([]);
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>🚀 Social Media Scraper & PDF Generator</h1>
        <p>Scrape TikTok and Instagram data, then generate beautiful PDF reports</p>
      </header>

      <main className="App-main">
        {/* Scraping Section */}
        <section className="scraping-section">
          <h2>📊 Data Scraping</h2>
          <ScrapingForm 
            onDataScraped={handleDataScraped}
            loading={loading}
            setLoading={setLoading}
          />
        </section>

        {/* Data Display Section */}
        {scrapedData.length > 0 && (
          <section className="data-section">
            <div className="section-header">
              <h2>📈 Scraped Data</h2>
              <button 
                className="clear-btn"
                onClick={clearData}
                title="Clear all data"
              >
                🗑️ Clear
              </button>
            </div>
            <DataDisplay data={scrapedData} />
          </section>
        )}

        {/* PDF Generation Section */}
        <section className="pdf-section">
          <h2>📄 PDF Generator</h2>
          <PDFGenerator scrapedData={scrapedData} />
        </section>

        {/* Instructions */}
        <section className="instructions">
          <h3>📝 How to Use</h3>
          <div className="instructions-grid">
            <div className="instruction-card">
              <h4>1. Scrape Data</h4>
              <p>Enter TikTok or Instagram URLs to extract performance metrics like views, likes, and comments.</p>
            </div>
            <div className="instruction-card">
              <h4>2. View Results</h4>
              <p>See the scraped data displayed in organized cards with all the extracted information.</p>
            </div>
            <div className="instruction-card">
              <h4>3. Generate PDF</h4>
              <p>Use the scraped data or custom JSON to create professional PDF reports for download.</p>
            </div>
          </div>
        </section>
      </main>

      <footer className="App-footer">
        <p>Built with React.js & Node.js | Powered by Puppeteer & PDFKit</p>
      </footer>
    </div>
  );
}

export default App;
