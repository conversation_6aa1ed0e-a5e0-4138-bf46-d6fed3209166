{"version": 3, "file": "InputStateManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/input/InputStateManager.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,MAAM,EAAC,MAAM,0BAA0B,CAAC;AAGhD,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAE3C,4CAA4C;AAC5C,wEAAwE;AACxE,MAAM,OAAO,iBAAkB,SAAQ,OAGtC;IACU,GAAG,CAAC,OAA4B;QACvC,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,UAAU,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,KAAK,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;IAC7B,CAAC;CACF"}