{"name": "pdfkit", "description": "A PDF generation library for Node.js", "keywords": ["pdf", "pdf writer", "pdf generator", "graphics", "document", "vector"], "version": "0.17.1", "homepage": "http://pdfkit.org/", "author": {"name": "Devon Govett", "email": "<EMAIL>", "url": "http://badassjs.com/"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/foliojs/pdfkit.git"}, "bugs": "https://github.com/foliojs/pdfkit/issues", "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-external-helpers": "^7.25.9", "@babel/preset-env": "^7.26.0", "@eslint/js": "^9.17.0", "@rollup/plugin-babel": "^6.0.4", "babel-jest": "^29.7.0", "blob-stream": "^0.1.3", "brace": "^0.11.1", "brfs": "~2.0.2", "browserify": "^17.0.1", "canvas": "^3.1.0", "codemirror": "~5.65.18", "eslint": "^9.17.0", "gh-pages": "^6.2.0", "globals": "^15.14.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-image-snapshot": "^6.4.0", "markdown": "~0.5.0", "pdfjs-dist": "^2.14.305", "prettier": "3.4.2", "pug": "^3.0.3", "rollup": "^2.79.2", "rollup-plugin-copy": "^3.5.0"}, "dependencies": {"crypto-js": "^4.2.0", "fontkit": "^2.0.4", "jpeg-exif": "^1.1.4", "linebreak": "^1.1.0", "png-js": "^1.0.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "rollup -c && npm run build-standalone", "build-standalone": "browserify --standalone PDFDocument --ignore crypto js/pdfkit.js > js/pdfkit.standalone.js", "browserify-example": "browserify examples/browserify/browser.js > examples/browserify/bundle.js", "pdf-guide": "node docs/generate.js", "website": "node docs/generate_website.js", "publish-website": "node docs/publish_website.js", "docs": "npm run pdf-guide && npm run website && npm run browserify-example", "lint": "eslint {lib,tests}/**/*.js", "prettier": "prettier lib tests docs", "test": "jest -i --env=node", "test:visual": "jest visual/ -i --env=node", "test:unit": "jest unit/ --env=node"}, "main": "js/pdfkit.js", "module": "js/pdfkit.es.js", "browserify": {"transform": ["brfs"]}, "engine": ["node >= v18.0.0"], "jest": {"testEnvironment": "jest-environment-jsdom", "testPathIgnorePatterns": ["/node_modules/", "<rootDir>/examples/"], "setupFilesAfterEnv": ["<rootDir>/tests/unit/setupTests.js"]}}