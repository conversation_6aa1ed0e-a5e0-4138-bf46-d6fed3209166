"use strict";
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PUPPETEER_REVISIONS = void 0;
/**
 * @internal
 */
exports.PUPPETEER_REVISIONS = Object.freeze({
    chrome: '137.0.7151.70',
    'chrome-headless-shell': '137.0.7151.70',
    firefox: 'stable_139.0.4',
});
//# sourceMappingURL=revisions.js.map